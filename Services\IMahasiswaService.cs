using PAA_Tugas2_AdeKurniawan_232410102041.Models;

namespace PAA_Tugas2_AdeKurniawan_232410102041.Services
{
    public interface IMahasiswaService
    {
        Task<IEnumerable<MahasiswaResponse>> GetAllMahasiswaAsync();
        Task<MahasiswaResponse?> GetMahasiswaByIdAsync(int id);
        Task<MahasiswaResponse> CreateMahasiswaAsync(CreateMahasiswaRequest request);
        Task<MahasiswaResponse?> UpdateMahasiswaAsync(int id, UpdateMahasiswaRequest request);
        Task<bool> DeleteMahasiswaAsync(int id);
        Task<bool> MahasiswaExistsAsync(int id);
    }
}
