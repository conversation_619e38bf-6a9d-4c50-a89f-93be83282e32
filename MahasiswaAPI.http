@baseUrl = http://localhost:5012
@contentType = application/json

### Get All Mahasiswa
GET {{baseUrl}}/api/mahasiswa
Accept: {{contentType}}

###

### Get <PERSON><PERSON><PERSON><PERSON> by ID
GET {{baseUrl}}/api/mahasiswa/1
Accept: {{contentType}}

###

### Create New Mahasiswa
POST {{baseUrl}}/api/mahasiswa
Content-Type: {{contentType}}

{
  "nama": "Budi Santoso",
  "alamat": "Jl. Kebon Jeruk No. 789, Surabaya",
  "email": "<EMAIL>"
}

###

### Update Mahasiswa
PUT {{baseUrl}}/api/mahasiswa/1
Content-Type: {{contentType}}

{
  "nama": "Ade Kurniawan Updated",
  "alamat": "Jl. Merdeka No. 123, Jakarta Selatan",
  "email": "<EMAIL>"
}

###

### Delete Mahasiswa
DELETE {{baseUrl}}/api/mahasiswa/2
Accept: {{contentType}}

###

### Test Invalid Email
POST {{baseUrl}}/api/mahasiswa
Content-Type: {{contentType}}

{
  "nama": "Test User",
  "alamat": "Test Address",
  "email": "invalid-email"
}

###

### Test Empty Fields
POST {{baseUrl}}/api/mahasiswa
Content-Type: {{contentType}}

{
  "nama": "",
  "alamat": "",
  "email": ""
}

###
