using Microsoft.AspNetCore.Mvc;
using PAA_Tugas2_AdeKurniawan_232410102041.Models;
using PAA_Tugas2_AdeKurniawan_232410102041.Services;
using System.ComponentModel.DataAnnotations;

namespace PAA_Tugas2_AdeKurniawan_232410102041.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MahasiswaController : ControllerBase
    {
        private readonly IMahasiswaService _mahasiswaService;
        private readonly ILogger<MahasiswaController> _logger;

        public MahasiswaController(IMahasiswaService mahasiswaService, ILogger<MahasiswaController> logger)
        {
            _mahasiswaService = mahasiswaService;
            _logger = logger;
        }

        /// <summary>
        /// Mendapatkan semua data mahasiswa
        /// </summary>
        /// <returns>List semua mahasiswa</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<MahasiswaResponse>>> GetAllMahasiswa()
        {
            try
            {
                _logger.LogInformation("Getting all mahasiswa data");
                var mahasiswa = await _mahasiswaService.GetAllMahasiswaAsync();
                return Ok(mahasiswa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting all mahasiswa");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Mendapatkan data mahasiswa berdasarkan ID
        /// </summary>
        /// <param name="id">ID mahasiswa</param>
        /// <returns>Data mahasiswa</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<MahasiswaResponse>> GetMahasiswaById(int id)
        {
            try
            {
                _logger.LogInformation("Getting mahasiswa with ID: {Id}", id);
                
                if (id <= 0)
                {
                    return BadRequest("ID harus lebih besar dari 0");
                }

                var mahasiswa = await _mahasiswaService.GetMahasiswaByIdAsync(id);
                
                if (mahasiswa == null)
                {
                    return NotFound($"Mahasiswa dengan ID {id} tidak ditemukan");
                }

                return Ok(mahasiswa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting mahasiswa with ID: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Membuat data mahasiswa baru
        /// </summary>
        /// <param name="request">Data mahasiswa baru</param>
        /// <returns>Data mahasiswa yang telah dibuat</returns>
        [HttpPost]
        public async Task<ActionResult<MahasiswaResponse>> CreateMahasiswa([FromBody] CreateMahasiswaRequest request)
        {
            try
            {
                _logger.LogInformation("Creating new mahasiswa");

                // Validasi input
                if (string.IsNullOrWhiteSpace(request.Nama))
                {
                    return BadRequest("Nama tidak boleh kosong");
                }

                if (string.IsNullOrWhiteSpace(request.Email))
                {
                    return BadRequest("Email tidak boleh kosong");
                }

                if (!IsValidEmail(request.Email))
                {
                    return BadRequest("Format email tidak valid");
                }

                if (string.IsNullOrWhiteSpace(request.Alamat))
                {
                    return BadRequest("Alamat tidak boleh kosong");
                }

                var mahasiswa = await _mahasiswaService.CreateMahasiswaAsync(request);
                
                return CreatedAtAction(
                    nameof(GetMahasiswaById), 
                    new { id = mahasiswa.Id }, 
                    mahasiswa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating mahasiswa");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Mengupdate data mahasiswa
        /// </summary>
        /// <param name="id">ID mahasiswa</param>
        /// <param name="request">Data mahasiswa yang akan diupdate</param>
        /// <returns>Data mahasiswa yang telah diupdate</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<MahasiswaResponse>> UpdateMahasiswa(int id, [FromBody] UpdateMahasiswaRequest request)
        {
            try
            {
                _logger.LogInformation("Updating mahasiswa with ID: {Id}", id);

                if (id <= 0)
                {
                    return BadRequest("ID harus lebih besar dari 0");
                }

                // Validasi input
                if (string.IsNullOrWhiteSpace(request.Nama))
                {
                    return BadRequest("Nama tidak boleh kosong");
                }

                if (string.IsNullOrWhiteSpace(request.Email))
                {
                    return BadRequest("Email tidak boleh kosong");
                }

                if (!IsValidEmail(request.Email))
                {
                    return BadRequest("Format email tidak valid");
                }

                if (string.IsNullOrWhiteSpace(request.Alamat))
                {
                    return BadRequest("Alamat tidak boleh kosong");
                }

                var mahasiswa = await _mahasiswaService.UpdateMahasiswaAsync(id, request);
                
                if (mahasiswa == null)
                {
                    return NotFound($"Mahasiswa dengan ID {id} tidak ditemukan");
                }

                return Ok(mahasiswa);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating mahasiswa with ID: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Menghapus data mahasiswa
        /// </summary>
        /// <param name="id">ID mahasiswa</param>
        /// <returns>Status penghapusan</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteMahasiswa(int id)
        {
            try
            {
                _logger.LogInformation("Deleting mahasiswa with ID: {Id}", id);

                if (id <= 0)
                {
                    return BadRequest("ID harus lebih besar dari 0");
                }

                var deleted = await _mahasiswaService.DeleteMahasiswaAsync(id);
                
                if (!deleted)
                {
                    return NotFound($"Mahasiswa dengan ID {id} tidak ditemukan");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting mahasiswa with ID: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        private static bool IsValidEmail(string email)
        {
            try
            {
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(email);
            }
            catch
            {
                return false;
            }
        }
    }
}
