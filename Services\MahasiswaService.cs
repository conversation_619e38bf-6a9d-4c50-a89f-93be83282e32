using PAA_Tugas2_AdeKurniawan_232410102041.Models;

namespace PAA_Tugas2_AdeKurniawan_232410102041.Services
{
    public class MahasiswaService : IMahasiswaService
    {
        private readonly List<Mahasiswa> _mahasiswaList;
        private int _nextId;

        public MahasiswaService()
        {
            _mahasiswaList = new List<Mahasiswa>();
            _nextId = 1;
            
            // Seed data untuk testing
            SeedData();
        }

        private void SeedData()
        {
            var mahasiswa1 = new Mahasiswa
            {
                Id = _nextId++,
                Nama = "Ade Kurniawan",
                Alamat = "Jl. Merdeka No. 123, Jakarta",
                Email = "<EMAIL>",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            var mahasiswa2 = new Mahasiswa
            {
                Id = _nextId++,
                Nama = "Siti Nurhaliza",
                Alamat = "Jl. Sudirman No. 456, Bandung",
                Email = "<EMAIL>",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _mahasiswaList.AddRange(new[] { mahasiswa1, mahasiswa2 });
        }

        public async Task<IEnumerable<MahasiswaResponse>> GetAllMahasiswaAsync()
        {
            await Task.Delay(1); // Simulate async operation
            
            return _mahasiswaList.Select(m => new MahasiswaResponse
            {
                Id = m.Id,
                Nama = m.Nama,
                Alamat = m.Alamat,
                Email = m.Email,
                CreatedAt = m.CreatedAt,
                UpdatedAt = m.UpdatedAt
            }).ToList();
        }

        public async Task<MahasiswaResponse?> GetMahasiswaByIdAsync(int id)
        {
            await Task.Delay(1); // Simulate async operation
            
            var mahasiswa = _mahasiswaList.FirstOrDefault(m => m.Id == id);
            if (mahasiswa == null)
                return null;

            return new MahasiswaResponse
            {
                Id = mahasiswa.Id,
                Nama = mahasiswa.Nama,
                Alamat = mahasiswa.Alamat,
                Email = mahasiswa.Email,
                CreatedAt = mahasiswa.CreatedAt,
                UpdatedAt = mahasiswa.UpdatedAt
            };
        }

        public async Task<MahasiswaResponse> CreateMahasiswaAsync(CreateMahasiswaRequest request)
        {
            await Task.Delay(1); // Simulate async operation
            
            var mahasiswa = new Mahasiswa
            {
                Id = _nextId++,
                Nama = request.Nama,
                Alamat = request.Alamat,
                Email = request.Email,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _mahasiswaList.Add(mahasiswa);

            return new MahasiswaResponse
            {
                Id = mahasiswa.Id,
                Nama = mahasiswa.Nama,
                Alamat = mahasiswa.Alamat,
                Email = mahasiswa.Email,
                CreatedAt = mahasiswa.CreatedAt,
                UpdatedAt = mahasiswa.UpdatedAt
            };
        }

        public async Task<MahasiswaResponse?> UpdateMahasiswaAsync(int id, UpdateMahasiswaRequest request)
        {
            await Task.Delay(1); // Simulate async operation
            
            var mahasiswa = _mahasiswaList.FirstOrDefault(m => m.Id == id);
            if (mahasiswa == null)
                return null;

            mahasiswa.Nama = request.Nama;
            mahasiswa.Alamat = request.Alamat;
            mahasiswa.Email = request.Email;
            mahasiswa.UpdatedAt = DateTime.Now;

            return new MahasiswaResponse
            {
                Id = mahasiswa.Id,
                Nama = mahasiswa.Nama,
                Alamat = mahasiswa.Alamat,
                Email = mahasiswa.Email,
                CreatedAt = mahasiswa.CreatedAt,
                UpdatedAt = mahasiswa.UpdatedAt
            };
        }

        public async Task<bool> DeleteMahasiswaAsync(int id)
        {
            await Task.Delay(1); // Simulate async operation
            
            var mahasiswa = _mahasiswaList.FirstOrDefault(m => m.Id == id);
            if (mahasiswa == null)
                return false;

            _mahasiswaList.Remove(mahasiswa);
            return true;
        }

        public async Task<bool> MahasiswaExistsAsync(int id)
        {
            await Task.Delay(1); // Simulate async operation
            return _mahasiswaList.Any(m => m.Id == id);
        }
    }
}
